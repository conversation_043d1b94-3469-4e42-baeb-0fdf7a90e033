{"version": "2.6", "projectName": "AIR720BLE", "projectType": "pure", "corePath": "c:\\Users\\<USER>\\AppData\\Roaming\\LuatIDE\\LuatideCore\\Air72XUX_CORE\\LuatOS-Air_V4017_RDA8910_BT_FLOAT.pac", "libPath": "c:\\Users\\<USER>\\AppData\\Roaming\\LuatIDE\\LuatideLib\\Air72XUX_LIB", "moduleModel": "air72XUX/air82XUX", "appFile": ["as.mp3", "ble.lua", "check.mp3", "commands.lua", "disconnected.mp3", "gpserror.mp3", "led.lua", "lock.mp3", "luatide_project.json", "main.lua", "my_utils.lua", "PinModule.lua", "SensorModule.lua", "SmsModule.lua", "success.mp3", "untar.mp3", "usermqtt.lua", "variables.lua"], "modulePort": "COM16", "ignore": ["produc_file\\0831.pac", "produc_file\\0831adc4.pac", "produc_file\\20240831.pac", "produc_file\\240831.pac", "produc_file\\240919.pac", "produc_file\\LuatOS-Air_V4017_RDA8910_BT_FLOAT.pac", "produc_file", "workspace.code-workspace"], "simulatorRun": "disable"}