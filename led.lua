--- 模块功能：zd控制
-- @module sensor
-- <AUTHOR>
-- @license None
-- @copyright XLL
-- @release 2022.09.02
module(..., package.seeall)

require 'sim'
require 'pins'
require 'socket'
require 'usermqtt'
local vars = require("variables")

local restart_count = 0
local network_led = 0
local disconnect_sound_count = 0 -- Counter for disconnect sound plays
local current_disconnect_session = false -- Flag to track current disconnection session

local function MP3_Play(filePath)
    log.info("MP3 Play", filePath) -- Log for debugging
    audio.play(4, "FILE", filePath, 7) -- Play the MP3 file with priority 4, volume 7
end

-- Function to play disconnection sound only once per session
local function playDisconnectSound()
    if vars.sound_flag and disconnect_sound_count < 1 then
        MP3_Play("/lua/disconnected.mp3") -- Play disconnected sound
        disconnect_sound_count = disconnect_sound_count + 1
        log.info("Disconnect Sound", "Played disconnection sound")
    elseif disconnect_sound_count >= 1 then
        log.info("Disconnect Sound", "Skipped playing disconnection sound (already played)")
    end
end

local function playDisconnectionAndRestart(reason)
    if vars.sound_flag and disconnect_sound_count < 1 then
        MP3_Play("/lua/disconnected.mp3") -- Play disconnected sound
        disconnect_sound_count = disconnect_sound_count + 1
        sys.wait(3000) -- Wait for 3 seconds to allow the sound to play
    end
    -- sys.restart(reason) -- Restart the device with the specified reason
end

function led_task()
    local netLED = pins.setup(pio.P0_4, 0); -- Network LED
    local cloundLED = pins.setup(pio.P0_1, 0); -- Cloud connection LED
    netLED(0) -- Turn off the network LED
    cloundLED(0) -- Turn off the cloud connection LED

    while true do
        if sim.getStatus() ~= true then -- No SIM card detected
            network_led = 0
            netLED(0) -- Turn off the network LED
            cloundLED(0) -- Turn off the cloud connection LED

            -- If we were previously connected, this is a new disconnection
            if mqtt_connected then
                current_disconnect_session = true
                disconnect_sound_count = 0 -- Reset counter for new disconnection session
            end

            mqtt_connected = false -- Reset connected flag
            mqtt_disconnected = false -- Reset disconnected flag
        else
            if socket.isReady() then
                restart_count = 0
                network_led = 1
                netLED(1) -- Turn on the network LED

                if usermqtt.isReady() then
                    cloundLED(1) -- Turn on the cloud connection LED
                    if not mqtt_connected then
                        if vars.sound_flag then
                            MP3_Play("/lua/success.mp3") -- Play success sound
                        end
                        mqtt_connected = true -- Set connected flag
                        mqtt_disconnected = false -- Reset disconnected flag
                        current_disconnect_session = false -- End of disconnection session
                        disconnect_sound_count = 0 -- Reset counter when connection is restored
                    end
                else
                    cloundLED(0) -- Turn off the cloud connection LED
                    if mqtt_connected then
                        -- Start of a new disconnection session
                        current_disconnect_session = true
                        disconnect_sound_count = 0 -- Reset counter for new disconnection
                        playDisconnectSound() -- Play sound with count limit
                        mqtt_disconnected = true -- Set disconnected flag
                        mqtt_connected = false -- Reset connected flag
                    end
                end
            else
                cloundLED(0)
                network_led = network_led == 0 and 1 or 0 -- Toggle the network LED state
                netLED(network_led) -- Blink the network LED
                if mqtt_connected then
                    -- Start of a new disconnection session
                    current_disconnect_session = true
                    disconnect_sound_count = 0 -- Reset counter for new disconnection
                    playDisconnectSound() -- Play sound with count limit
                    mqtt_disconnected = true -- Set disconnected flag
                    mqtt_connected = false -- Reset connected flag
                elseif mqtt_disconnected and current_disconnect_session then
                    -- We're already in a disconnection session, check if we should play sound
                    playDisconnectSound() -- This will only play if count < 3
                end
            end
        end

        restart_count = restart_count + 1
        if restart_count >= (60 * 2) then -- If SIM is not detected or socket is not ready for 1 minute
            playDisconnectionAndRestart("socket ready overtime or sim remove") -- Play sound and restart
        end
        sys.wait(500) -- Wait 500ms
    end
end

sys.taskInit(led_task) -- 启动LED协程
