local MyUtils = {}
local vars = require('variables')
local PinModule = require('PinModule')  -- Import PinModule
local SensorModule = require('SensorModule')  -- Import SensorModule

function MyUtils.packFormattedData()
    -- Reuse the working packJsonData method to get the sensor data
    local jsonData = MyUtils.packJsonData()
    local success, decodedJson = pcall(json.decode, jsonData)

    -- Handle decoding errors
    if not success then
        log.error("PackFormattedData", "Failed to decode JSON data")
        return "Error packing data"
    end

    -- Extract relevant data from decoded JSON
    local latitude = decodedJson["Lat"]
    local longitude = decodedJson["Lon"]
    local voltage = tonumber(decodedJson["volt"]) or 0
    local temperature = tonumber(decodedJson["temp"]) or 0
    local humidity = tonumber(decodedJson["hum"]) or 0

    -- Check if GPS data is available; if not, display "GPS signal lost"
    local gpsMessage
    if not latitude or not longitude or (latitude == "0 N" and longitude == "0 E") then
        gpsMessage = "GPS signal lost"
    else
        -- Remove spaces between latitude/longitude and their directional indicators
        latitude = latitude:gsub(" ", "")
        longitude = longitude:gsub(" ", "")
        gpsMessage = string.format("https://maps.google.com/?q=%s,%s", latitude, longitude)
    end

    -- Format the data into the desired message, including the Google Maps link or GPS lost message
    local formattedMessage = string.format(
        "Tanii mashin:\n%s\nBatt: %.2fV | Temp: %.1fC\nHum: %.1f%%",
        gpsMessage, voltage, temperature, humidity
    )

    -- Log the formatted message for debugging
    log.info("PackFormattedData", formattedMessage)

    return formattedMessage -- Return the formatted string
end

-- Function to send data with a specific command
function MyUtils.sendData(command)
    -- Helper function to get the emoji associated with a command
    local function getEmojiForCommand(cmd)
        local emojis = {
            lock = 1,
            unlock = 3,
            mirror = 4,
            as = 6,
            check = 3
        }
        return emojis[cmd] or 0 -- Default case if the command doesn't match
    end

    -- Validate input command
    if not command or type(command) ~= "string" then
        log.error("sendCommandData", "Invalid command: " .. tostring(command))
        return -1
    end

    -- Get the CSV data
    local csvData = MyUtils.packCsvData()

    -- Check if CSV data is valid
    if not csvData then
        log.error("sendCommandData", "Failed to pack CSV data. Aborting send operation.")
        return -1
    end

    -- Get emoji associated with the command
    local emoji = getEmojiForCommand(command)

    -- Combine the CSV data, emoji, and command into a single message
    local message = csvData .. "," .. tostring(emoji) .. "," .. command
    log.debug("sendCommandData", "Constructed message: " .. message)

    -- Convert the message string to a table of byte values (payload)
    local payload = {}
    for i = 1, #message do
        payload[i] = string.byte(message, i)
    end

    -- Attempt to send data via BLE to the ESP32
    local result = ble.BLE_Read_Data(vars.device_name, payload)

    -- Check the result and handle accordingly
    if result == -1 then
        log.error("BLE", "Failed to send data to device named: " .. vars.device_name)
    else
        log.info("BLE", "Data sent to device successfully: " .. vars.device_name)
    end

    return result
end

-- Function to pack sensor and state data into JSON
function MyUtils.packJsonData()
    local jsonData = {}
    local humidity, temperature = SensorModule.readHumidityTemperature() -- Read temperature and humidity
    local latitude, longitude, speed = SensorModule.readGPS() -- Read GPS data

    -- Check if gps_flag is false, and set GPS values to 0, 0, 0 if so
    if not vars.gps_flag then
        latitude, longitude, speed = 0, 0, 0
    end

    jsonData["temp"] = math.floor(temperature + 0.5) -- Temperature
    jsonData["hum"] = math.floor(humidity + 0.5) -- Humidity
    jsonData["Lat"] = latitude -- Latitude
    jsonData["Lon"] = longitude -- Longitude
    jsonData["Speed"] = speed -- Speed
    jsonData["volt"] = SensorModule.readVoltage() -- Battery voltage

    jsonData["motion"] = PinModule.readPinState("S1") == 1 and 0 or 1
    jsonData["light"] = PinModule.readPinState("S2") == 1 and 0 or 1
    jsonData["ver"] = VERSION
    jsonData["rssi"] = net.getRssi()

    local encodedJson = json.encode(jsonData) -- Encode as JSON
    log.info("PackJsonData", "JSON data packed: " .. encodedJson) -- Log debug info
    return encodedJson -- Return JSON data
end

-- Function to pack sensor and state data into CSV format
function MyUtils.packCsvData()
    local jsonData = MyUtils.packJsonData()
    local success, decodedJson = pcall(json.decode, jsonData)

    if not success then
        log.error("PackCsvData", "Failed to decode JSON data")
        return nil
    end

    local voltage = decodedJson["volt"]
    local temperature = decodedJson["temp"]
    local status = decodedJson["sta"]

    local csvData = string.format("%d,%d,%d", voltage, temperature, status)
    log.info("PackCsvData", "Packed Data: " .. csvData)
    return csvData -- Return CSV formatted data
end

-- Function to check if a file exists
function MyUtils.fileExists(filename)
    local file = io.open(filename, "r")
    if file then
        io.close(file)
        return true
    else
        return false
    end
end

-- Function to write data to a file
function MyUtils.writeToFile(filename, data)
    local file, err = io.open(filename, "w")
    if not file then
        log.error("writeToFile", "Failed to open file for writing: " .. tostring(err))
        return false
    end

    -- Convert data to string if it's not already a string
    if type(data) == "boolean" then
        data = tostring(data)
    elseif type(data) ~= "string" and type(data) ~= "number" then
        log.warn("writeToFile", "Converting non-string/non-number data to string: " .. type(data))
        data = tostring(data)
    end

    file:write(data)
    file:close()
    return true
end

-- Function to read data from a file
function MyUtils.readFile(filename)
    local file, err = io.open(filename, "r")
    if not file then
        log.error("readFile", "Failed to open file for reading: " .. tostring(err))
        return nil
    end
    local content = file:read("*a")
    file:close()
    return content
end

-- Function to create a directory with fallback options
function MyUtils.createDirectory(dirPath)
    -- Check if directory already exists
    if MyUtils.fileExists(dirPath) then
        return true
    end

    -- Try using rtos.make_dir first
    local success, err = pcall(rtos.make_dir, dirPath)
    if success then
        log.info("FileSystem", "Successfully created directory " .. dirPath .. " with rtos.make_dir")
        return true
    else
        log.warn("RTOS", "Failed to create directory with rtos.make_dir: " .. (err or "Unknown error"))

        -- Try using io.mkdir as a fallback
        if io.mkdir then
            local success2, err2 = pcall(io.mkdir, dirPath)
            if success2 then
                log.info("FileSystem", "Successfully created directory " .. dirPath .. " with io.mkdir")
                return true
            else
                log.error("IO", "Failed to create directory with io.mkdir: " .. (err2 or "Unknown error"))
            end
        else
            log.warn("IO", "io.mkdir function not available on this device")
        end
    end

    -- Try to write a test file to see if the directory exists or can be created implicitly
    local testPath = dirPath .. "/.test"
    local success3, err3 = pcall(MyUtils.writeToFile, testPath, "test")
    if success3 then
        -- If we can write to the directory, it exists or was created implicitly
        -- Clean up the test file
        os.remove(testPath)
        log.info("FileSystem", "Directory " .. dirPath .. " appears to be usable")
        return true
    else
        log.error("FileSystem", "Directory " .. dirPath .. " cannot be created or used: " .. (err3 or "Unknown error"))
        return false
    end
end

-- Function to safely send MQTT messages with retries
function MyUtils.safeMqttSend(topic, message, qos, max_retries, retry_delay)
    -- Set default values if not provided
    max_retries = max_retries or 3
    retry_delay = retry_delay or 1000 -- 1 second
    qos = qos or 0

    -- Check if usermqtt module is available
    if not usermqtt then
        log.error("MQTT", "usermqtt module not available")
        return false, "usermqtt module not available"
    end

    -- Try to send the message with retries
    local sent = false
    local last_error = nil

    for i = 1, max_retries do
        log.info("MQTT", "Sending message attempt " .. i .. " to topic: " .. topic)

        local success, err = pcall(usermqtt.insertMsg, topic, message, qos)

        if success then
            log.info("MQTT", "Message sent successfully to topic: " .. topic)
            sent = true
            break
        else
            last_error = err or "Unknown error"
            log.error("MQTT", "Failed to send message (attempt " .. i .. "): " .. last_error)

            -- Wait before retrying
            if i < max_retries then
                sys.wait(retry_delay)
            end
        end
    end

    if not sent then
        log.error("MQTT", "Failed to send message after " .. max_retries .. " attempts")
        return false, last_error
    end

    return true
end

-- Function to check MQTT connection status
function MyUtils.checkMqttStatus()
    -- Get MQTT connection status
    local mqtt_connected = false
    local mqtt_error = "Unknown"

    if usermqtt and usermqtt.isConnected then
        local success, is_connected = pcall(usermqtt.isConnected)
        if success then
            mqtt_connected = is_connected
        end
    end

    -- Get last MQTT error if available
    if usermqtt and usermqtt.getLastError then
        local success, last_error = pcall(usermqtt.getLastError)
        if success and last_error then
            mqtt_error = last_error
        end
    end

    -- Get network status
    local net_status = "Unknown"
    if net and net.getState then
        local success, state = pcall(net.getState)
        if success then
            net_status = state
        end
    end

    -- Get signal strength
    local signal = "Unknown"
    if net and net.getRssi then
        local success, rssi = pcall(net.getRssi)
        if success then
            signal = rssi
        end
    end

    -- Return diagnostic info
    return {
        mqtt_connected = mqtt_connected,
        mqtt_error = mqtt_error,
        network_status = net_status,
        signal_strength = signal,
        client_id = usermqtt.clientID and usermqtt.clientID() or "Unknown",
        uptime = os.time(),
        memory_free = collectgarbage("count")
    }
end

return MyUtils
