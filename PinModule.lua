-- PinModule.lua

local PinModule = {}

-- Initialize pins
function PinModule.setupPins()
    PinModule.pins = {
        S1 = pio.P0_5,   -- GPIO5 as input with pull-up, S1
        S2 = pio.P0_13,  -- GPIO13 as input with pull-up, S2
        Relay1 = pio.P0_18,  -- GPIO18 as output, low, R1
        Relay2 = pio.P0_19,  -- GPIO19 as output, low, R2
        Relay3 = pio.P0_0,  -- GPIO58 as output, low, R2

        Go2 = pio.P0_22,  -- GPIO22 as output, low, R3
        Go3 = pio.P0_24,  -- GPIO24 as output, low, R3

        Key1 = pio.P0_28,    -- GPIO28 as output, low, R42
        Key2 = pio.P0_27     -- GPIO29 as output, low, R31
    }

    pins.setup(PinModule.pins.S1, nil, pio.PULLUP)
    pins.setup(PinModule.pins.S2, nil, pio.PULLUP)
    PinModule.relayControl("Relay1", 0)
    PinModule.relayControl("Relay2", 0)
    PinModule.relayControl("Relay3", 0)

    PinModule.relayControl("Go2", 0)
    PinModule.relayControl("Go3", 0)
    PinModule.relayControl("Key1", 0)
    PinModule.relayControl("Key2", 0)
end

-- Control relays
function PinModule.relayControl(relayName, state)
    local relayPin = PinModule.pins[relayName]
    if relayPin then
        pins.setup(relayPin, state)
    else
        print("Error: Relay " .. relayName .. " is not defined.")
    end
end

-- Read the state of a pin
function PinModule.readPinState(pinName)
    local pin = PinModule.pins[pinName]
    if pin then
        return pio.pin.getval(pin)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
        return nil
    end
end

return PinModule
