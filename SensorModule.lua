--- 模块功能：传感器控制
-- @module SensorModule
-- @release 2022.09.02
local SensorModule = {}
require 'sys'
require 'log'

-- Safely try to load the GPS module
local gps
local gps_available = true
local success, err = pcall(function()
    gps = require "gpsZkw"
end)

if not success then
    log.warn("GPS", "Failed to load GPS module: " .. (err or "Unknown error"))
    gps_available = false
end

-- Internal function to initialize the AHT20 sensor
local function aht20_init()
    local secdat, thirdat
    local dat = i2c.recv(1, 0x38, 1) -- 读取AHT20状态寄存器
    local _, state = pack.unpack(dat, "b1")
    if state == nil then
        log.error("AHT20", "Failed to read sensor state")
        return
    end
    log.info("AHT20 State", string.format("%02X", state))

    if state ~= 0x18 then
        log.info("AHT20 State", "Calibrating the sensor")
        i2c.send(1, 0x38, {0x1B, 0x00, 0x00})
        sys.wait(5)
        dat = i2c.recv(1, 0x38, 3)
        sys.wait(10)
        _, firdat, secdat, thirdat = pack.unpack(dat, "b3")
        i2c.send(1, 0x38, {bit.bor(0xB0, 0x1B), secdat, thirdat})
        sys.wait(10)

        i2c.send(1, 0x38, {0x1C, 0x00, 0x00})
        sys.wait(5)
        dat = i2c.recv(1, 0x38, 3)
        sys.wait(10)
        _, _, secdat, thirdat = pack.unpack(dat, "b3")
        i2c.send(1, 0x38, {bit.bor(0xB0, 0x1C), secdat, thirdat})
        sys.wait(10)

        i2c.send(1, 0x38, {0x1E, 0x00, 0x00})
        sys.wait(5)
        dat = i2c.recv(1, 0x38, 3)
        sys.wait(10)
        _, firdat, secdat, thirdat = pack.unpack(dat, "b3")
        i2c.send(1, 0x38, {bit.bor(0xB0, 0x1E), secdat, thirdat})
        sys.wait(10)
    end
end

-- Initialize sensors (I2C, ADC, GPS)
function SensorModule.init()
    -- Initialize I2C
    i2c.setup(1, i2c.SLOW) -- 初始化IIC1

    -- Initialize ADC
    adc.open(3, adc.SCALE_3V233) -- 打开ADC3, ADC范围0~3.233V
    adc.open(4, adc.SCALE_3V233) -- 打开ADC3, ADC范围0~3.233V

    -- Initialize GPS (run in a separate coroutine to avoid yielding issues)
    sys.taskInit(function()
        -- Initialize AHT20 sensor first
        sys.wait(150) -- 等待AHT20就绪
        aht20_init() -- 初始化AHT20

        -- Only initialize GPS if the module is available
        if gps_available then
            log.info("GPS", "Initializing GPS...")

            -- Use pcall to safely call GPS functions
            local success, err = pcall(function()
                gps.setUart(3, 9600, 8, uart.PAR_NONE, uart.STOP_1) -- 设置GPS串口参数
                gps.open(gps.DEFAULT, {
                    tag = "gpsOpen"
                }) -- 开启GPS
                gps.setNmeaMode(0) -- lua 内部处理GPS数据
            end)

            if success then
                log.info("GPS", "GPS initialization complete.")
            else
                log.error("GPS", "Failed to initialize GPS: " .. (err or "Unknown error"))
                gps_available = false
            end
        else
            log.warn("GPS", "GPS module not available, skipping initialization")
        end

        sys.wait(500) -- 等待外设就绪
    end)
end

-- Read battery voltage using ADC
function SensorModule.readVoltage()
    -- Read from ADC3 and ADC4
    local adcval3, voltage3 = adc.read(3)
    local adcval4, voltage4 = adc.read(4)

    -- Check if both voltages are valid
    if voltage3 and voltage4 then
        -- Determine the greater voltage between the two
        local greaterVoltage = (voltage3 > voltage4) and voltage3 or voltage4
        local adcval = (voltage3 > voltage4) and adcval3 or adcval4

        -- Log the greater voltage
        log.info("Greater Voltage", "ADC Value:", adcval, "Voltage (mV):", greaterVoltage)

        -- Apply the scaling formula: voltage * 5.7 / 1000 to convert to volts
        local calculatedVoltage = (greaterVoltage * 5.7) / 1000
        
        -- Apply the voltage offset if available
        local vars = require("variables")
        if vars and vars.voltage_offset then
            calculatedVoltage = calculatedVoltage + vars.voltage_offset
            log.info("Adjusted Voltage (V):", calculatedVoltage, "Offset:", vars.voltage_offset)
        else
            log.info("Calculated Voltage (V):", calculatedVoltage, "No offset applied")
        end

        return calculatedVoltage
    else
        log.error("ADC", "Failed to read voltage")
        return nil
    end
end

-- Read humidity and temperature from AHT20 sensor
function SensorModule.readHumidityTemperature()
    local humi, temp = 0, 0
    local retries = 5
    local success = false

    while retries > 0 do
        i2c.send(1, 0x38, {0xAC, 0x33, 0x00})
        sys.wait(150)
        local dat = i2c.recv(1, 0x38, 6)
        if #dat == 6 then
            -- Unpack and process data
            local _, _, data2, data3, data4, data5, data6 = pack.unpack(dat, "b6")
            humi = bit.bor(bit.bor(bit.lshift(data2, 12), bit.lshift(data3, 4)), bit.rshift(data4, 4)) / 1048576 * 10000
            temp = bit.bor(bit.bor(bit.lshift(bit.band(data4, 0x0f), 16), bit.lshift(data5, 8)), data6) / 1048576 * 20000 - 5000
            log.info("AHT20", "Humidity: ", humi / 100)
            log.info("AHT20", "Temperature: ", temp / 100)
            success = true
            break
        else
            retries = retries - 1
            sys.wait(150)
        end
    end

    if not success then
        return nil, nil
    end

    return humi / 100, temp / 100
end

-- Check if GPS is available
function SensorModule.isGPSAvailable()
    return gps_available
end

-- Read GPS data (latitude, longitude, speed)
function SensorModule.readGPS()
    -- Check if GPS module is available
    if not gps_available then
        log.warn("GPS", "GPS module not available")
        return "0 N", "0 E", 0
    end

    -- Use pcall to safely call GPS functions
    local success, result = pcall(function()
        local kmspped, nmspeed = gps.getSpeed() -- 获取速度
        local location = gps.getLocation() -- 获取经纬度信息
        local is_fixed = gps.isFix()

        log.info("GPS", "Fix: ", is_fixed)

        if is_fixed == true then
            log.info("GPS", "Location: ", location.lat, location.latType, location.lng, location.lngType)
            return {
                lat = string.format("%.5f %s", location.lat, location.latType),
                lng = string.format("%.5f %s", location.lng, location.lngType),
                speed = kmspped
            }
        else
            log.info("GPS", "GPS not fixed, unable to get location")
            return {
                lat = "0 N",
                lng = "0 E",
                speed = 0
            }
        end
    end)

    if success then
        return result.lat, result.lng, result.speed
    else
        log.error("GPS", "Error reading GPS data: " .. (result or "Unknown error"))
        return "0 N", "0 E", 0
    end
end

return SensorModule
